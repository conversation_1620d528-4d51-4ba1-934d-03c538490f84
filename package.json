{"name": "skilltest-new", "version": "1.0.0", "description": "A platform to create skill tests for users.", "main": "dist/server.js", "scripts": {"dev": "nodemon --watch src -e ts,hbs src/server.ts", "build": "tsc && terser src/views/test/attend.js -o src/public/js/attend.js -c -m", "copy-views": "mkdir -p dist/views && cp -R src/views dist", "copy-public": "mkdir -p dist/public && cp -R src/public dist", "prebuild": "rm -rf dist", "build:ts": "tsc", "migration:run:build": "npm run build:ts && npm run migration:run", "start": "ts-node src/server.ts", "typeorm": "typeorm-ts-node-commonjs", "migration:create": "node create-migration.js", "migration:generate": "node generate-migration.js", "migration:run": "typeorm-ts-node-commonjs migration:run -d src/database/connection.ts", "migration:revert": "pnpm run typeorm migration:revert -- -d src/database/connection.ts"}, "repository": {"type": "git", "url": "git+https://github.com/qwillio-live/skilltest-new.git"}, "keywords": ["skilltest", "<PERSON><PERSON><PERSON>"], "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/qwillio-live/skilltest-new/issues"}, "homepage": "https://github.com/qwillio-live/skilltest-new#readme", "dependencies": {"@fastify/cors": "^9.0.1", "@fastify/formbody": "^7.4.0", "@fastify/helmet": "^11.1.1", "@fastify/secure-session": "^7.5.1", "@fastify/sensible": "^5.6.0", "@fastify/static": "^7.0.4", "@fastify/view": "^9.1.0", "dotenv": "^16.4.5", "fastify": "^4.28.1", "fastify-helmet": "^7.1.0", "handlebars": "^4.7.8", "mysql2": "^3.11.2", "reflect-metadata": "^0.2.2", "typeorm": "^0.3.20", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^22.5.4", "nodemon": "^3.1.4", "terser": "^5.33.0", "ts-node": "^10.9.2", "typescript": "^5.6.2"}}