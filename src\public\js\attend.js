const startScript=()=>{const e={visibilityAPI:void 0!==document.hidden,clipboardAPI:void 0!==navigator.clipboard,deviceMemory:void 0!==navigator.deviceMemory,hardwareConcurrency:void 0!==navigator.hardwareConcurrency,webGL:!!window.WebGLRenderingContext,audioContext:!(!window.AudioContext&&!window.webkitAudioContext),intl:"undefined"!=typeof Intl},t=Date.now();let n=t,r="",a=[],o=[],i=[],s=[],c=[],d=null,u=null,l={},m="desktop";const p=window.trackingConfig||{},g=!p.disableFocusLostEvents&&e.visibilityAPI,v=!p.disableClipboardEvents,w=!p.disablePreSubmitDelay,h=!p.disableAnswerChangeEvents,y=!p.disableDeviceFingerprint,f=!p.disableTimeToFirstInteraction,b=!p.disableMouseClickEvents,_=!p.disableKeyboardPressEvents;fetch("https://api.ipify.org").then((e=>e.text())).then((e=>{r=e}));if(m=(()=>{const e=navigator.userAgent.toLowerCase(),t=/mobile|android|iphone|ipad|phone/i.test(e);return/tablet|ipad/i.test(e)?"tablet":t?"mobile":"desktop"})(),g){let e=null;const t=()=>{document.hidden?e=Date.now():e&&(a.push({timestamp:e,duration_ms:Date.now()-e}),e=null)},n=()=>{e||(e=Date.now())},r=()=>{e&&(a.push({timestamp:e,duration_ms:Date.now()-e}),e=null)};document.addEventListener("visibilitychange",t),window.addEventListener("blur",n),window.addEventListener("focus",r)}if(v){const e=()=>{const e=window.getSelection().toString().trim();e.length>0&&o.push({timestamp:Date.now(),type:"copy",content:e.substring(0,100)})},t=e=>{const t=(e.clipboardData?.getData("text/plain")||"").trim();t.length>0&&o.push({timestamp:Date.now(),type:"paste",content:t.substring(0,100)})},n=()=>{const e=window.getSelection().toString().trim();e.length>0&&o.push({timestamp:Date.now(),type:"cut",content:e.substring(0,100)})};document.addEventListener("copy",e),document.addEventListener("paste",t),document.addEventListener("cut",n)}if(h){const e=(e,t,n)=>{const r=document.querySelector('input[name="question_id"]').value;i.push({question_id:parseInt(r),previous_answer:t,new_answer:n,timestamp:Date.now(),input_type:e.type||"text"}),u=Date.now()},t=t=>{if(("answer"===t.target.name||t.target.name?.startsWith("answer-"))&&"radio"!==t.target.type&&"checkbox"!==t.target.type){const n=t.target.dataset.previousValue||"";e(t.target,n,t.target.value),t.target.dataset.previousValue=t.target.value}},n=t=>{if(("radio"===t.target.type||"checkbox"===t.target.type)&&("answer"===t.target.name||t.target.name?.startsWith("answer-"))){const n=t.target.dataset.previousValue||"";e(t.target,n,t.target.value),t.target.dataset.previousValue=t.target.value}};document.addEventListener("input",t),document.addEventListener("change",n)}if(b){const e=((e,t)=>{let n;return function(){const r=arguments,a=this;n||(e.apply(a,r),n=!0,setTimeout((()=>n=!1),t))}})((e=>{s.push({timestamp:Date.now(),button:0===e.button?"left":2===e.button?"right":"middle",x:e.clientX,y:e.clientY,target:e.target.tagName+(e.target.id?"#"+e.target.id:"")})}),100);document.addEventListener("click",e),document.addEventListener("contextmenu",e)}if(_){const e=((e,t)=>{let n;return function(){const r=arguments,a=this;n||(e.apply(a,r),n=!0,setTimeout((()=>n=!1),t))}})((e=>{let t="other";e.key.match(/^[a-zA-Z]$/)?t="letter":e.key.match(/^[0-9]$/)?t="number":"Backspace"===e.key?t="backspace":"Enter"===e.key?t="enter":" "===e.key?t="space":e.key.startsWith("Arrow")?t="navigation":(e.ctrlKey||e.metaKey||e.altKey)&&(t="modifier"),c.push({timestamp:Date.now(),keyType:t,key:e.key})}),100);document.addEventListener("keydown",e)}if(f){const e=()=>{d||(d=Date.now()-t,document.removeEventListener("click",e),document.removeEventListener("keydown",e),document.removeEventListener("input",e))};document.addEventListener("click",e),document.addEventListener("keydown",e),document.addEventListener("input",e)}if(y){(async()=>{const t={};t.os=navigator.userAgentData?.platform||navigator.platform||"unknown",t.browser=navigator.userAgentData?.brands?.[0]?.brand||navigator.userAgent.match(/(Chrome|Firefox|Safari|Edge)\/[\d.]+/)?.[0]||"Unknown",t.screenResolution=`${screen.width}x${screen.height}`,t.colorDepth=screen.colorDepth,t.deviceMemory=e.deviceMemory?navigator.deviceMemory||"unknown":"unsupported",t.hardwareConcurrency=e.hardwareConcurrency?navigator.hardwareConcurrency||"unknown":"unsupported",t.timezone=e.intl?Intl.DateTimeFormat().resolvedOptions().timeZone:"unsupported",t.language=navigator.language,t.platform=navigator.userAgentData?.platform||navigator.platform||"unknown",t.cookieEnabled=navigator.cookieEnabled;try{const e=document.createElement("canvas"),n=e.getContext("2d");n.textBaseline="top",n.font="14px Arial",n.fillText("Device fingerprint test 🔒",2,2),t.canvasFingerprint=e.toDataURL().substring(0,100)}catch(e){t.canvasFingerprint="unavailable"}try{const e=document.createElement("canvas"),n=e.getContext("webgl")||e.getContext("experimental-webgl");if(n){const e=n.getExtension("WEBGL_debug_renderer_info");t.webglVendor=e?n.getParameter(e.UNMASKED_VENDOR_WEBGL):"unknown",t.webglRenderer=e?n.getParameter(e.UNMASKED_RENDERER_WEBGL):"unknown",t.gpu=t.webglRenderer}else t.webglVendor="unavailable",t.webglRenderer="unavailable",t.gpu="unavailable"}catch(e){t.webglVendor="error",t.webglRenderer="error",t.gpu="error"}if(e.audioContext)try{const e=new(window.AudioContext||window.webkitAudioContext),n=e.createOscillator(),r=e.createAnalyser(),a=e.createGain();n.type="triangle",n.frequency.setValueAtTime(1e4,e.currentTime),a.gain.setValueAtTime(0,e.currentTime),n.connect(r),r.connect(a),a.connect(e.destination),n.start(0),setTimeout((()=>{const a=new Float32Array(r.frequencyBinCount);r.getFloatFrequencyData(a);let o=0;for(let e=0;e<a.length;e++)o+=Math.abs(a[e]);t.audioFingerprint=o.toString(),n.stop(),e.close()}),100)}catch(e){t.audioFingerprint="error"}else t.audioFingerprint="unsupported";try{const e=["monospace","sans-serif","serif"],n=["Arial","Arial Black","Arial Narrow","Arial Rounded MT Bold","Calibri","Cambria","Comic Sans MS","Consolas","Courier","Courier New","Georgia","Helvetica","Impact","Lucida Console","Lucida Sans Unicode","Microsoft Sans Serif","MS Gothic","MS PGothic","MS Sans Serif","MS Serif","Palatino Linotype","Segoe UI","Tahoma","Times","Times New Roman","Trebuchet MS","Verdana","Wingdings"],r="mmmmmmmmmmlli",a="72px",o=document.getElementsByTagName("body")[0],i=document.createElement("span");i.style.fontSize=a,i.innerHTML=r;const s={},c={};for(let t=0;t<e.length;t++)i.style.fontFamily=e[t],o.appendChild(i),s[e[t]]=i.offsetWidth,c[e[t]]=i.offsetHeight,o.removeChild(i);const d=[];for(let t=0;t<n.length;t++){let r=!1;for(let a=0;a<e.length;a++){i.style.fontFamily=n[t]+","+e[a],o.appendChild(i);const d=i.offsetWidth!==s[e[a]]||i.offsetHeight!==c[e[a]];o.removeChild(i),r=r||d}r&&d.push(n[t])}t.fonts=d}catch(e){t.fonts=["detection_failed"]}return t})().then((e=>{l=e})).catch((()=>{l={os:navigator.userAgentData?.platform||navigator.platform||"unknown",browser:navigator.userAgentData?.brands?.[0]?.brand||navigator.userAgent.match(/(Chrome|Firefox|Safari|Edge)\/[\d.]+/)?.[0]||"Unknown",screenResolution:`${screen.width}x${screen.height}`,colorDepth:screen.colorDepth,deviceMemory:e.deviceMemory?navigator.deviceMemory||"unknown":"unsupported",hardwareConcurrency:e.hardwareConcurrency?navigator.hardwareConcurrency||"unknown":"unsupported",timezone:e.intl?Intl.DateTimeFormat().resolvedOptions().timeZone:"unsupported",language:navigator.language,platform:navigator.userAgentData?.platform||navigator.platform||"unknown",error:"comprehensive_fingerprinting_failed"}}))}let E=0,k=0,S=0,C=0;if(document.addEventListener("visibilitychange",(()=>{document.hidden?n=Date.now():E+=Date.now()-n})),v){const e=()=>k++,t=()=>S++;document.addEventListener("copy",e),document.addEventListener("paste",t)}if(b){const e=()=>C++;document.addEventListener("contextmenu",e)}document.getElementById("answerForm").addEventListener("submit",(async e=>{e.preventDefault();const n=document.querySelector('input[name="answer_type"]')?.value;let p;if("radiobutton"===n?p=document.querySelector('input[name="answer"]:checked')?.value:"multiinput"===n?p=Array.from(document.querySelectorAll('input[name="answer"]:checked')).map((e=>e.value)):"textarea"===n?p=document.querySelector('textarea[name="answer"]').value.trim():"multiTextInput"===n&&(p=Array.from(document.querySelectorAll('input[name^="answer-"]')).map((e=>e.value.trim())).filter((e=>""!==e))),!p||Array.isArray(p)&&0===p.length)return void alert("Please provide an answer before submitting.");const D=document.querySelector('input[name="test_id"]')?.value,A=document.querySelector('input[name="question_id"]')?.value,L=document.querySelector('input[name="user_id"]')?.value,x=Date.now()-t,M=w&&u?(Date.now()-u)/1e3:0,T=(e,t=1e3)=>e.slice(0,t),F={test_id:D,question_id:A,profile_id:L,answer:p,time_taken:Math.round(x/1e3),ip:r,copy_count:k,paste_count:S,right_click_count:C,inactive_time:Math.round(E/1e3),start_time:new Date(t).toISOString(),submit_time:(new Date).toISOString(),device_type:m};g&&(F.focus_lost_events=T(a)),v&&(F.clipboard_events=T(o)),w&&(F.pre_submit_delay=M),h&&(F.answer_change_events=T(i)),y&&(F.device_fingerprint=l),f&&(F.time_to_first_interaction=d?d/1e3:0),b&&(F.mouse_click_events=T(s)),_&&(F.keyboard_press_events=T(c));const q=document.querySelector('input[name="result_salt"]')?.value,I=((e,t)=>{const n=JSON.stringify(e);let r="";for(let e=0;e<n.length;e++)r+=String.fromCharCode(n.charCodeAt(e)^t.charCodeAt(e%t.length));return btoa(r)})(F,L+q);try{if(!(await fetch(e.target.action,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded",trc:navigator.userAgent},body:"hashed_payload="+encodeURIComponent(I)})).ok)throw new Error("Server responded with an error");window.location.reload()}catch(e){console.error("Error:",e),alert("An error occurred. Please try again.")}}))};startScript();