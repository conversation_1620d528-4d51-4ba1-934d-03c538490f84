{"$schema": "https://json.schemastore.org/tsconfig", "display": "<PERSON><PERSON><PERSON>", "compilerOptions": {"declaration": true, "declarationMap": true, "esModuleInterop": true, "incremental": false, "noEmit": false, "isolatedModules": true, "useDefineForClassFields": true, "lib": ["ESNext", "DOM", "DOM.Iterable"], "module": "CommonJS", "moduleResolution": "node", "noUncheckedIndexedAccess": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "ES2022", "outDir": "dist", "rootDir": "src", "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictPropertyInitialization": false}, "include": ["src/**/*"], "exclude": ["node_modules"]}