<div class="header-container">
  <h1>All Groups</h1>
  <button id="openModalBtn" class="btn btn-blue">Create New Group</button>
</div>

<div class="table-container">
  <table>
    <thead>
      <tr>
        <th>Group ID</th>
        <th>Group Name</th>
        <th>Creation Date</th>
        <th>User Count</th>
        <th>Countries of Users</th>
        <th>Test IDs</th>
      </tr>
    </thead>
    <tbody>
      {{#each groups}}
      <tr>
        <td data-label="Group ID">
          {{this.id}}
          <br>
          <a href="/admin/group/test_result?group_id={{this.id}}&key={{../key}}" class="view-results-link">View Results</a>
        </td>
        <td data-label="Group Name">{{this.name}}</td>
        <td data-label="Creation Date">{{formatDate this.createdAt}}</td>
        <td data-label="User Count">{{this.profile_count}}</td>
        <td data-label="Countries">{{this.profile_countries}}</td>
        <td data-label="Test IDs">{{this.test_ids}}</td>
      </tr>
      {{/each}}
    </tbody>
  </table>
</div>

<div id="createGroupModal" class="modal">
  <div class="modal-content" style="max-width: 400px;">
    <button class="modal-close" id="closeModal" aria-label="Close modal">
      <i class="fas fa-times"></i>
    </button>
    <div class="modal-header">
      <h2>Create New Group</h2>
    </div>
    <div class="modal-body">
      <form id="createGroupForm">
        <div class="form-div">
          <label for="group_id">Group ID:</label>
          <input type="number" id="group_id" name="group_id" class="form-control" placeholder="Enter Group ID" required>
        </div>
        <div class="form-div">
          <label for="group_name">Group Name:</label>
          <input type="text" id="group_name" name="group_name" class="form-control" placeholder="Enter Group Name" required>
        </div>
        <div id="formMessage" class="mt-3 text-center"></div>
      </form>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-gray" id="cancelModal">Cancel</button>
      <button type="submit" form="createGroupForm" class="btn btn-blue">Create</button>
    </div>
  </div>
</div>

<script src="/js/allGroups.js"></script>

<script>
  document.addEventListener('DOMContentLoaded', function() {
  const modal = document.getElementById('createGroupModal');
  const formMessage = document.getElementById('formMessage');
  const createGroupForm = document.getElementById('createGroupForm');

  // Close modal
  document.querySelectorAll('#closeModal, #cancelModal').forEach(button => {
    button.addEventListener('click', () => {
      modal.classList.remove('active');
      modal.style.display = 'none';
    });
  });
  });

  document.getElementById('createGroupForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const formMessage = document.getElementById('formMessage');

    fetch('/admin/group/create?key={{this.key}}', {
      method: 'POST',
      body: new URLSearchParams(formData),
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        formMessage.textContent = data.message;
        formMessage.style.color = 'green';
        setTimeout(() => {
          location.reload();
        }, 1500);
      } else {
        formMessage.textContent = data.message;
        formMessage.style.color = 'red';
      }
    })
    .catch(error => {
      console.error('Error:', error);
      formMessage.textContent = 'An error occurred. Please try again.';
      formMessage.style.color = 'red';
    });
  });
</script>

<style>
  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  .modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.4);
  }

  .modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
  }

  .view-results-link {
    font-size: 0.9em;
    text-decoration: none;
    color: #007bff;
  }

  .view-results-link:hover {
    text-decoration: underline;
  }
</style>