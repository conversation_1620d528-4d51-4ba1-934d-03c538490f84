/**
 * Tracking event interfaces for user behavior tracking
 * These interfaces define the structure of events tracked during test taking
 */

/**
 * Event triggered when a user changes their answer
 */
export interface AnswerChangeEvent {
    question_id: number;
    previous_answer: string;
    new_answer: string;
    timestamp: number;
    input_type: string;
}

/**
 * Event triggered when user loses focus on the test window
 */
export interface FocusLostEvent {
    timestamp: number;
    duration_ms: number;
}

/**
 * Event triggered when user gains or loses focus on the test window
 */
export interface FocusEvent {
    timestamp: number;
    duration_ms: number;
    type: "active" | "inactive";
}

/**
 * Event triggered when user clicks the mouse
 */
export interface MouseClickEvent {
    timestamp: number;
    button: string;
    x: number;
    y: number;
    target: string;
}

/**
 * Event triggered when user presses a key
 */
export interface KeyboardPressEvent {
    timestamp: number;
    keyType: string;
    key: string; // The actual key/letter pressed (e.g., 'a', 'Enter', 'Shift')
}

/**
 * Event triggered for clipboard operations (copy/paste/cut)
 */
export interface ClipboardEvent {
    timestamp: number;
    type: "copy" | "paste" | "cut";
    content: string;
}

/**
 * Answer data structure including tracking events
 */
export interface Answer {
    question_id: number;
    question: string;
    answer: string;
    correct?: string;
    is_correct: boolean;
    time_taken: number;
    inactive_time: number;
    pre_submit_delay?: number;
    time_to_first_interaction?: number;
    copy_count: number;
    paste_count: number;
    right_click_count: number;
    start_time?: Date | string | null;
    submit_time?: Date | string | null;
    clipboard_events?: ClipboardEvent[];
    answer_change_events?: AnswerChangeEvent[];
    focus_lost_events?: FocusLostEvent[];
    focus_events?: FocusEvent[];
    mouse_click_events?: MouseClickEvent[];
    keyboard_press_events?: KeyboardPressEvent[];
}

/**
 * Tracking configuration options
 */
export interface TrackingConfig {
    disableFocusEvents?: boolean;
    disableMouseClickEvents?: boolean;
    disableKeyboardPressEvents?: boolean;
    disableDeviceFingerprint?: boolean;
    disableClipboardEvents?: boolean;
    disableAnswerChangeEvents?: boolean;
    disablePreSubmitDelay?: boolean;
    disableTimeToFirstInteraction?: boolean;
}

/**
 * Base test summary statistics
 */
export interface BaseSummary {
    total_questions: number;
    total_time: number;
    total_inactive_time: number;
    average_time_per_question: number;
    // Optional tracking metrics (only included if tracked)
    total_focus_lost_count?: number;
    total_focus_lost_duration?: number;
}

/**
 * Math test summary statistics for AI export
 */
export interface MathTestSummary extends BaseSummary {
    correct_answers: number;
    score_percentage: number;
    total_copy_actions: number;
    total_paste_actions: number;
    total_right_click_actions: number;
    total_answer_changes?: number;
    questions_without_changes?: number;
    questions_with_multiple_attempts?: number;
    total_mouse_clicks?: number;
    total_keyboard_presses?: number;
}

/**
 * JavaScript test summary statistics for AI export
 */
export interface JavaScriptTestSummary extends BaseSummary {
    average_answer_length: number;
    average_word_count: number;
}
