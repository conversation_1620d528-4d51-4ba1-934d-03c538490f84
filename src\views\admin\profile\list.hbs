<div class="header-container">
  <h1 class="text-2xl font-bold">View Profiles</h1>
</div>

{{#if error}}
  <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4" role="alert">
    <p>{{error}}</p>
  </div>
{{/if}}

{{#if profiles.length}}
  <table>
    <thead>
      <tr>
        <th>ID</th>
        <th>Name</th>
        <th>Email</th>
        <th>Created At</th>
        <th>Updated At</th>
      </tr>
    </thead>
    <tbody>
      {{#each profiles}}
        <tr>
          <td data-label="ID"><a href="/admin/profile/view?id={{this.link}}&key={{../key}}">{{this.link}}</a></td>
          <td data-label="Name">{{this.name}}</td>
          <td data-label="Email">{{this.email}}</td>
          <td data-label="Created At">{{formatDate this.createdAt}}</td>
          <td data-label="Updated At">{{formatDate this.updatedAt}}</td>
        </tr>
      {{/each}}
    </tbody>
  </table>

  <div class="mt-4 flex justify-between items-center">
    <div>
      Page {{currentPage}} of {{totalPages}}
    </div>
    <div>
      {{#if hasPrevPage}}
        <a href="/admin/profile/list?page={{subtract currentPage 1}}&key={{this.key}}" class="btn btn-blue">Previous</a>
      {{/if}}
      {{#if hasNextPage}}
        <a href="/admin/profile/list?page={{add currentPage 1}}&key={{this.key}}" class="btn btn-blue">Next</a>
      {{/if}}
    </div>
  </div>
{{else}}
  <p class="text-gray-600">No profiles found.</p>
{{/if}}

<style>
  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
</style>