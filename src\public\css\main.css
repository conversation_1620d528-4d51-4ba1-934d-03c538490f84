/* ===== RESET AND BASE STYLES ===== */
* {
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    margin: 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* ===== RESPONSIVE BREAKPOINTS ===== */
/* Mobile: 320px - 767px */
/* Tablet: 768px - 1023px */
/* Desktop: 1024px+ */

/* ===== LAYOUT COMPONENTS ===== */
.container {
    background-color: white;
    border-radius: 8px;
    min-height: 100vh;
    max-width: 100vw;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 0;
    margin: 0;
}

.content-wrapper {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
}

@media (min-width: 768px) {
    .content-wrapper {
        padding: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .content-wrapper {
        padding: 2rem;
    }
}

/* ===== HEADER STYLES ===== */
.header {
    background-color: #3498db;
    color: white;
    padding: 1rem;
    border-radius: 8px 8px 0 0;
    position: relative;
}

.header h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

@media (min-width: 768px) {
    .header {
        padding: 1.5rem;
    }
    
    .header h1 {
        font-size: 1.875rem;
    }
}

/* ===== NAVIGATION STYLES ===== */
.nav {
    margin-top: 0.75rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.nav a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 0.75rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    font-size: 0.875rem;
    white-space: nowrap;
}

.nav a:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

@media (min-width: 768px) {
    .nav {
        gap: 1rem;
    }
    
    .nav a {
        font-size: 1rem;
        padding: 0.5rem 1rem;
    }
}

/* ===== MOBILE NAVIGATION ===== */
.mobile-nav-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    position: absolute;
    top: 1rem;
    right: 1rem;
}

@media (max-width: 767px) {
    .mobile-nav-toggle {
        display: block;
    }
    
    .nav {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: #2980b9;
        flex-direction: column;
        padding: 1rem;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .nav.active {
        display: flex;
    }
    
    .nav a {
        display: block;
        padding: 0.75rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .nav a:last-child {
        border-bottom: none;
    }
}

/* ===== BUTTON STYLES ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 44px; /* Touch-friendly minimum */
    min-width: 44px;
    text-align: center;
    line-height: 1.2;
}

.btn:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

/* Button variants */
.btn-primary {
    background-color: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background-color: #45a049;
}

.btn-blue {
    background-color: #3b82f6;
    color: white;
}

.btn-blue:hover {
    background-color: #2563eb;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-gray {
    background-color: #6c757d;
    color: white;
}

.btn-gray:hover {
    background-color: #5a6268;
}

/* Button sizes */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    min-height: 36px;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    min-height: 52px;
}

@media (max-width: 767px) {
    .btn {
        padding: 0.875rem 1.25rem;
        font-size: 1rem;
        min-height: 48px; /* Larger touch targets on mobile */
    }
    
    .btn-sm {
        padding: 0.625rem 1rem;
        min-height: 40px;
    }
}

/* ===== FORM STYLES ===== */
.form-container {
    max-width: 100%;
    margin: 0 auto;
}

@media (min-width: 768px) {
    .form-container {
        max-width: 600px;
    }
}

.form-div {
    margin-bottom: 1.5rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    font-size: 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    background-color: white;
    min-height: 44px; /* Touch-friendly */
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-control:disabled {
    background-color: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
}

@media (min-width: 768px) {
    label {
        font-size: 1rem;
    }
}

/* Form text helpers */
.form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.text-muted {
    color: #6c757d;
}

/* Select styling */
select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    appearance: none;
}

/* Textarea styling */
textarea.form-control {
    resize: vertical;
    min-height: 100px;
    font-family: inherit;
}

/* ===== TABLE STYLES ===== */
.table-container {
    overflow-x: auto;
    margin: 1rem 0;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    min-width: 600px; /* Minimum width for horizontal scroll */
}

th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
    vertical-align: top;
}

@media (min-width: 768px) {
    th, td {
        padding: 1rem;
    }
}

th {
    background-color: #f8f9fa;
    color: #374151;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.75rem;
    position: sticky;
    top: 0;
    z-index: 1;
}

@media (min-width: 768px) {
    th {
        font-size: 0.875rem;
    }
}

tr:last-child td {
    border-bottom: none;
}

tr:nth-child(even) {
    background-color: #f9fafc;
}

tr:hover {
    background-color: #f0f4f8;
    transition: background-color 0.2s ease;
}

/* Table images */
td img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
}

/* ===== RESPONSIVE TABLE FOR MOBILE ===== */
@media screen and (max-width: 767px) {
    .table-container {
        overflow-x: visible;
    }
    
    table {
        min-width: auto;
    }
    
    table, thead, tbody, th, td, tr {
        display: block;
    }
    
    thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }
    
    tr {
        border: 1px solid #e0e0e0;
        margin-bottom: 1rem;
        border-radius: 8px;
        overflow: hidden;
        background-color: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    td {
        border: none;
        position: relative;
        padding: 0.75rem 0.75rem 0.75rem 40%;
        text-align: right;
        word-break: break-word;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }
    
    td:before {
        content: attr(data-label) ": ";
        position: absolute;
        left: 0.75rem;
        width: 35%;
        padding-right: 0.5rem;
        white-space: nowrap;
        text-align: left;
        font-weight: 600;
        color: #374151;
        display: flex;
        align-items: center;
    }
    
    /* Special handling for certain columns */
    td[data-label="Countries"], 
    td[data-label="Test IDs"], 
    td[data-label="Question"],
    td[data-label="Question Details"] {
        text-align: left;
        padding-left: 0.75rem;
    }
    
    td[data-label="Countries"]:before,
    td[data-label="Test IDs"]:before,
    td[data-label="Question"]:before,
    td[data-label="Question Details"]:before {
        position: static;
        display: block;
        width: 100%;
        margin-bottom: 0.5rem;
        text-align: left;
    }
}

/* ===== MODAL STYLES ===== */
.modal {
    display: none;
    position: fixed;
    z-index: 99999 !important;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
    animation: fadeIn 0.2s ease;
}

/* Ensure modals moved to body are always on top */
body > .modal {
    z-index: 999999 !important;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.modal-content {
    background-color: white;
    border-radius: 12px;
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    z-index: 100000 !important;
    animation: slideIn 0.3s ease;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

@media (min-width: 768px) {
    .modal-content {
        max-width: 600px;
        padding: 2rem;
    }
}

.modal-header {
    padding: 1.5rem 1.5rem 0;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 1.5rem;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
}

.modal-body {
    padding: 0 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
    flex-wrap: wrap;
}

@media (max-width: 767px) {
    .modal-footer {
        flex-direction: column;
    }

    .modal-footer .btn {
        width: 100%;
    }
}

.modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6b7280;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background-color: #f3f4f6;
    color: #374151;
}

/* Modal animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-5 { margin-top: 1.25rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-5 { margin-bottom: 1.25rem; }

.ml-1 { margin-left: 0.25rem; }
.mr-1 { margin-right: 0.25rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }

.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }

.rounded { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }

.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

/* ===== RESPONSIVE GRID ===== */
.grid {
    display: grid;
    gap: 1rem;
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }

@media (min-width: 768px) {
    .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
    .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

/* ===== EXPORT ICONS ===== */
.export-icons {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-left: 0.5rem;
}

.export-icon {
    cursor: pointer;
    font-size: 1.1rem;
    color: #3b82f6;
    transition: color 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
}

.export-icon:hover {
    color: #1d4ed8;
    background-color: #eff6ff;
}

.export-icon.disabled {
    color: #d1d5db !important;
    cursor: not-allowed !important;
    background-color: transparent !important;
}

/* ===== SNACKBAR/TOAST ===== */
.snackbar {
    position: fixed;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    background-color: #1f2937;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    z-index: 1100;
    animation: slideUp 0.3s ease, slideDown 0.3s ease 2.7s;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    max-width: calc(100vw - 2rem);
    text-align: center;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(100%);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
    to {
        opacity: 0;
        transform: translateX(-50%) translateY(100%);
    }
}

/* ===== SPECIFIC COMPONENT STYLES ===== */

/* Header container for pages */
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

@media (max-width: 767px) {
    .header-container {
        flex-direction: column;
        align-items: stretch;
    }
}

/* Template content display */
.template-content {
    max-width: 100%;
    white-space: pre-wrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 100px;
    word-break: break-word;
}

/* Template preview */
.template-preview {
    width: 100%;
    max-width: 100%;
    resize: vertical;
    padding: 1rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    box-sizing: border-box;
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
}

/* Questions container */
#questions-container {
    margin-bottom: 1rem;
    max-height: 400px;
    overflow-y: auto;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

@media (min-width: 768px) {
    #questions-container {
        max-height: 600px;
    }
}

/* Result container */
#resultContainer {
    margin-top: 2rem;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

/* Generate button special styling */
#generateBtn {
    padding: 0.875rem 2rem;
    font-size: 1.125rem;
    font-weight: 600;
    margin-top: 8px;
    min-width: 200px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

#generateBtn:hover {
    box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
}

@media (max-width: 768px) {
    #generateBtn {
        margin: 0 auto 1.5rem auto;
    }
}

/* Tracking options */
.tracking-options {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
    margin: 1rem 0;
}

@media (min-width: 768px) {
    .tracking-options {
        grid-template-columns: repeat(2, 1fr);
    }
}

.tracking-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background-color: #f9fafb;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.tracking-option input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #3b82f6;
}

.tracking-option label {
    margin: 0;
    font-weight: 400;
    cursor: pointer;
    flex: 1;
}

/* Toggle tracking button */
.toggle-tracking {
    margin-bottom: 1rem;
    width: 100%;
}

@media (min-width: 768px) {
    .toggle-tracking {
        width: auto;
    }
}

/* Test container styles */
.test-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    background-color: #f9fafb;
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem;
    border-radius: 12px;
    text-align: center;
}

.test-title {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: #111827;
}

.test-description {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    color: #6b7280;
    max-width: 600px;
    line-height: 1.6;
}

@media (max-width: 767px) {
    .test-title {
        font-size: 1.5rem;
    }

    .test-description {
        font-size: 1rem;
    }
}

/* Test attend page styles */
.flex-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    max-width: 800px;
    margin: 0 auto;
}

.question-container {
    flex: 1;
    margin-bottom: 2rem;
}

#question-div {
    max-width: 100%;
    overflow-x: auto;
    padding: 1rem;
    background-color: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    margin-bottom: 1.5rem;
}

.submit-container {
    margin-top: 2rem;
    text-align: center;
    padding: 1rem;
}

#submitButton {
    background-color: #2563eb;
    color: white;
    font-weight: 600;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1.125rem;
    min-width: 150px;
}

/* View results link */
.view-results-link {
    color: #3b82f6;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 0.5rem;
    display: inline-block;
}

.view-results-link:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Focus styles for accessibility */

/* ===== CREATE TEST PAGE STYLES ===== */

/* Freelancer input and generate button row */
.freelancer-generate-row {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-bottom: 1.5rem;
}

.freelancer-input-container {
    flex: 1;
    width: 100%;
}

.freelancer-input-container .form-div {
    margin-bottom: 0;
}

.generate-button-container {
    flex-shrink: 0;
}

#generateBtn {
    height: 44px;
    padding: 0 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    white-space: nowrap;
    border-radius: 6px;
    transition: all 0.2s ease;
    min-width: 140px;
}

#generateBtn:hover {
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

/* Mobile responsive layout */
@media (max-width: 767px) {
    .freelancer-generate-row {
        flex-direction: column;
        gap: 1rem;
    }

    .generate-button-container {
        width: 100%;
    }

    #generateBtn {
        width: 100%;
        height: 48px; /* Larger touch target on mobile */
        min-width: auto;
    }
}

/* Result container positioning */
.result-container {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

/* Alert styles */
.alert {
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

/* Question item styles */
.question-item {
    background-color: #ffffff;
    margin-bottom: 1.25rem;
    position: relative;
    padding: 1.25rem;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.question-item.readonly {
    background-color: #f8f9fa;
    border-left: 4px solid #6c757d;
}

.question-item.readonly textarea,
.question-item.readonly input,
.question-item.readonly select {
    background-color: #e9ecef;
    cursor: not-allowed;
}

.question-item .form-group {
    margin-bottom: 1rem;
}

.question-item .form-group:last-child {
    margin-bottom: 0;
}

.remove-question {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

/* Generated message textarea */
#generatedMessage {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
}

/* Form group styles */
.form-group {
    margin-bottom: 1rem;
}

/* Responsive adjustments for tablet */
@media (min-width: 768px) and (max-width: 1023px) {
    .freelancer-generate-row {
        gap: 1.5rem;
    }

    #generateBtn {
        min-width: 160px;
    }
}
*:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

button:focus,
.btn:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .btn, .export-icons, .modal, .snackbar {
        display: none !important;
    }

    .container {
        box-shadow: none;
        border-radius: 0;
    }

    table {
        border-collapse: collapse;
    }

    th, td {
        border: 1px solid #000;
    }
}
